/**
 * Enhanced Loading States
 * Sistema unificado de estados de carga con animaciones profesionales
 * Incluye skeleton loaders, spinners y feedback contextual
 */

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ViewStyle, ActivityIndicator } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { Sparkles, Brain, Eye, Palette } from 'lucide-react-native';

import DesignSystem from '@/constants/DesignSystem';
import Colors from '@/constants/colors';
import { radius, typography } from '@/constants/theme';

const { colors, spacing } = DesignSystem;

interface LoadingStateProps {
  variant?: 'spinner' | 'skeleton' | 'pulse' | 'shimmer' | 'contextual';
  size?: 'sm' | 'base' | 'lg' | 'xl';
  message?: string;
  context?: 'ai-analysis' | 'photo-processing' | 'formula-generation' | 'general';
  showIcon?: boolean;
  fullScreen?: boolean;
  style?: ViewStyle;
}

export const EnhancedLoadingState: React.FC<LoadingStateProps> = ({
  variant = 'spinner',
  size = 'base',
  message,
  context = 'general',
  showIcon = true,
  fullScreen = false,
  style,
}) => {
  const animationValue = useSharedValue(0);
  const pulseValue = useSharedValue(1);
  const rotateValue = useSharedValue(0);

  useEffect(() => {
    // Start animations based on variant
    switch (variant) {
      case 'shimmer':
        animationValue.value = withRepeat(
          withTiming(1, { duration: 1500, easing: Easing.ease }),
          -1,
          false
        );
        break;
      case 'pulse':
        pulseValue.value = withRepeat(
          withSequence(withTiming(1.05, { duration: 800 }), withTiming(1, { duration: 800 })),
          -1,
          false
        );
        break;
      case 'contextual':
        rotateValue.value = withRepeat(
          withTiming(360, { duration: 2000, easing: Easing.linear }),
          -1,
          false
        );
        pulseValue.value = withRepeat(
          withSequence(withTiming(1.1, { duration: 1000 }), withTiming(1, { duration: 1000 })),
          -1,
          false
        );
        break;
    }
  }, [variant, animationValue, pulseValue, rotateValue]);

  const shimmerStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(animationValue, [0, 1], [-100, 300]),
      },
    ],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseValue }],
  }));

  const rotateStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotateValue}deg` }],
  }));

  const getSizeStyle = () => {
    switch (size) {
      case 'sm':
        return styles.size_sm;
      case 'base':
        return styles.size_base;
      case 'lg':
        return styles.size_lg;
      case 'xl':
        return styles.size_xl;
      default:
        return styles.size_base;
    }
  };

  const getMessageSizeStyle = () => {
    switch (size) {
      case 'sm':
        return styles.message_sm;
      case 'base':
        return styles.message_base;
      case 'lg':
        return styles.message_lg;
      default:
        return styles.message_base;
    }
  };

  const containerStyle = [styles.container, getSizeStyle(), fullScreen && styles.fullScreen, style];

  const getContextualIcon = () => {
    switch (context) {
      case 'ai-analysis':
        return Brain;
      case 'photo-processing':
        return Eye;
      case 'formula-generation':
        return Palette;
      default:
        return Sparkles;
    }
  };

  const getContextualMessage = () => {
    if (message) return message;

    switch (context) {
      case 'ai-analysis':
        return 'Analizando imagen con IA...';
      case 'photo-processing':
        return 'Procesando imagen...';
      case 'formula-generation':
        return 'Generando fórmula personalizada...';
      default:
        return 'Cargando...';
    }
  };

  if (variant === 'skeleton') {
    return (
      <View testID="loading-skeleton">
        <SkeletonLoader size={size} style={style} />
      </View>
    );
  }

  if (variant === 'shimmer') {
    return (
      <View
        testID="loading-shimmer"
        style={[styles.shimmerContainer, containerStyle]}
        accessible={true}
        accessibilityRole="progressbar"
        accessibilityLabel={message || 'Cargando con efecto shimmer'}
      >
        <View style={styles.shimmerBox}>
          <Animated.View style={[styles.shimmerOverlay, shimmerStyle]} />
        </View>
        {message && <Text style={styles.message}>{message}</Text>}
      </View>
    );
  }

  const IconComponent = getContextualIcon();

  return (
    <View
      testID="loading-state"
      style={containerStyle}
      accessible={true}
      accessibilityRole="progressbar"
      accessibilityLabel={getContextualMessage()}
    >
      {showIcon && (
        <Animated.View
          testID="loading-icon"
          style={[styles.iconContainer, pulseStyle, rotateStyle]}
        >
          {variant === 'contextual' ? (
            <View testID="loading-contextual">
              <IconComponent size={getIconSize(size)} color={colors.primary} />
            </View>
          ) : (
            <View testID="loading-spinner">
              <ActivityIndicator size={size === 'sm' ? 'small' : 'large'} color={colors.primary} />
            </View>
          )}
        </Animated.View>
      )}

      {variant === 'pulse' && (
        <Animated.View testID="loading-pulse" style={[styles.pulseRing, pulseStyle]} />
      )}

      <Text style={[styles.message, getMessageSizeStyle()]}>{getContextualMessage()}</Text>

      {context !== 'general' && <Text style={styles.contextHint}>{getContextHint(context)}</Text>}
    </View>
  );
};

// Skeleton Loader Component
export const SkeletonLoader: React.FC<{
  variant?: 'line' | 'card' | 'avatar' | 'button' | 'custom';
  width?: number | string;
  height?: number | string;
  size?: 'sm' | 'base' | 'lg';
  style?: ViewStyle;
  lines?: number;
}> = ({ variant = 'line', width, height, size = 'base', style, lines = 1 }) => {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1200, easing: Easing.ease }),
      -1,
      false
    );
  }, [shimmerValue]);

  const shimmerStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: interpolate(shimmerValue.value, [0, 1], [-100, 100]),
      },
    ],
  }));

  const getSkeletonDimensions = () => {
    switch (variant) {
      case 'avatar':
        return { width: 40, height: 40, borderRadius: radius.full };
      case 'button':
        return { width: 120, height: 44, borderRadius: radius.md };
      case 'card':
        return { width: '100%', height: 120, borderRadius: radius.xl };
      default:
        return {
          width: width || '100%',
          height: height || getSizeHeight(size),
          borderRadius: radius.sm,
        };
    }
  };

  const dimensions = getSkeletonDimensions();

  if (variant === 'custom') {
    return (
      <View style={[styles.skeletonBase, dimensions, style]}>
        <Animated.View style={[styles.skeletonShimmer, shimmerStyle]} />
      </View>
    );
  }

  return (
    <View style={style}>
      {Array.from({ length: lines }).map((_, index) => (
        <View
          key={index}
          style={[
            styles.skeletonBase,
            dimensions,
            index < lines - 1 && { marginBottom: spacing['2'] },
          ]}
        >
          <Animated.View style={[styles.skeletonShimmer, shimmerStyle]} />
        </View>
      ))}
    </View>
  );
};

// Card Skeleton for service listings
export const ServiceCardSkeleton: React.FC = () => (
  <View style={styles.cardSkeleton}>
    <View style={styles.cardHeader}>
      <SkeletonLoader variant="avatar" size="sm" />
      <View style={styles.cardHeaderText}>
        <SkeletonLoader width="60%" height={16} />
        <SkeletonLoader width="80%" height={12} />
      </View>
    </View>
    <SkeletonLoader lines={2} height={14} style={styles.skeletonMarginTop} />
    <View style={styles.cardActions}>
      <SkeletonLoader variant="button" size="sm" />
      <SkeletonLoader variant="button" size="sm" />
    </View>
  </View>
);

// Helper functions
const getIconSize = (size: string): number => {
  switch (size) {
    case 'sm':
      return 20;
    case 'base':
      return 28;
    case 'lg':
      return 36;
    case 'xl':
      return 48;
    default:
      return 28;
  }
};

const getSizeHeight = (size: string): number => {
  switch (size) {
    case 'sm':
      return 12;
    case 'base':
      return 16;
    case 'lg':
      return 20;
    default:
      return 16;
  }
};

const getContextHint = (context: string): string => {
  switch (context) {
    case 'ai-analysis':
      return 'Analizando niveles de color y estructura...';
    case 'photo-processing':
      return 'Optimizando calidad de imagen...';
    case 'formula-generation':
      return 'Calculando proporciones exactas...';
    default:
      return '';
  }
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  fullScreen: {
    flex: 1,
    backgroundColor: colors.background,
  },

  size_sm: {
    padding: spacing['3'],
  },

  size_base: {
    padding: spacing['6'],
  },

  size_lg: {
    padding: spacing['8'],
  },

  size_xl: {
    padding: spacing['10'],
  },

  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['4'],
  },

  pulseRing: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: radius.full,
    borderWidth: 2,
    borderColor: colors.primary + '30',
    backgroundColor: Colors.common.transparent,
  },

  message: {
    textAlign: 'center',
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },

  message_sm: {
    fontSize: typography.sizes.sm,
  },

  message_base: {
    fontSize: typography.sizes.base,
  },

  message_lg: {
    fontSize: typography.sizes.lg,
  },

  contextHint: {
    fontSize: typography.sizes.xs,
    color: colors.text.tertiary,
    textAlign: 'center',
    marginTop: spacing['2'],
    fontStyle: 'italic',
  },

  // Shimmer styles
  shimmerContainer: {
    alignItems: 'center',
  },

  shimmerBox: {
    backgroundColor: colors.neutral[200],
    borderRadius: radius.md,
    overflow: 'hidden',
    width: 200,
    height: 20,
    marginBottom: spacing['3'],
  },

  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.background,
    opacity: 0.6,
    width: 100,
  },

  // Skeleton styles
  skeletonBase: {
    backgroundColor: colors.neutral[200],
    overflow: 'hidden',
    position: 'relative',
  },

  skeletonShimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.background,
    opacity: 0.5,
    width: 100,
  },

  // Card skeleton styles
  cardSkeleton: {
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['4'],
    marginBottom: spacing['3'],
    ...DesignSystem.shadows.sm,
  },

  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  cardHeaderText: {
    flex: 1,
    marginLeft: spacing['3'],
  },

  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing['4'],
  },
  skeletonMarginTop: {
    marginTop: spacing['3'],
  },
});
