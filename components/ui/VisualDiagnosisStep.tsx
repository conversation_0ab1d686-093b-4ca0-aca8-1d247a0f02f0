/**
 * Visual Diagnosis Step Component
 * Rediseño del SimpleDiagnosisStep con enfoque visual y mobile-first
 * Elimina formularios complejos a favor de interacciones intuitivas
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  FadeIn,
  FadeOut,
  SlideInRight,
} from 'react-native-reanimated';
import {
  Camera,
  Zap,
  Check,
  Sparkles,
  Eye,
  Target,
  Award,
  TrendingUp,
  RefreshCw,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';

import DesignSystem from '@/constants/DesignSystem';
import { EnhancedButton } from './EnhancedButton';
import { shadows, radius, typography, animations } from '@/constants/theme';

const { colors, typography, spacing, radius, shadows, animations } = DesignSystem;
const { width: _SCREEN_WIDTH, height: _SCREEN_HEIGHT } = Dimensions.get('window');

interface VisualDiagnosisStepProps {
  data: any;
  onUpdate: (updates: any) => void;
  onNext: () => void;
  onBack: () => void;
}

type DiagnosisPhase = 'capture' | 'analysis' | 'validation' | 'confirmation';

interface PhotoCapture {
  id: string;
  uri: string;
  type: 'roots' | 'mids' | 'ends';
  timestamp: Date;
}

interface AIAnalysisResult {
  overallLevel: number;
  overallTone: string;
  confidence: number;
  zoneAnalysis: {
    roots: { level: number; tone: string; grayPercentage: number };
    mids: { level: number; tone: string; condition: string };
    ends: { level: number; tone: string; damage: string };
  };
  recommendations: string[];
}

export const VisualDiagnosisStep: React.FC<VisualDiagnosisStepProps> = ({
  data,
  onUpdate,
  onNext,
  onBack,
}) => {
  const [currentPhase, setCurrentPhase] = useState<DiagnosisPhase>('capture');
  const [captures, setCaptures] = useState<PhotoCapture[]>([]);
  const [analysisResult, setAnalysisResult] = useState<AIAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Animation values
  const progressValue = useSharedValue(0);
  const pulseScale = useSharedValue(1);
  const successScale = useSharedValue(1);

  // Simulate photo capture
  const handlePhotoCapture = async (type: 'roots' | 'mids' | 'ends') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Simulate camera capture
    const newCapture: PhotoCapture = {
      id: `${type}_${Date.now()}`,
      uri: `mock://photo_${type}.jpg`,
      type,
      timestamp: new Date(),
    };

    setCaptures(prev => {
      const filtered = prev.filter(c => c.type !== type);
      return [...filtered, newCapture];
    });

    // Trigger success animation
    successScale.value = withSequence(
      withSpring(1.1, animations.spring.bouncy),
      withSpring(1, animations.spring.gentle)
    );

    // Auto-advance to analysis after all 3 photos
    if (captures.length === 2) {
      setTimeout(() => startAnalysis(), 1000);
    }
  };

  const startAnalysis = async () => {
    setCurrentPhase('analysis');
    setIsAnalyzing(true);

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Animate progress
    progressValue.value = withTiming(1, { duration: 3000 });

    // Pulse animation during analysis
    pulseScale.value = withSequence(
      withTiming(1.05, { duration: 800 }),
      withTiming(1, { duration: 800 })
    );

    // Simulate AI analysis
    setTimeout(() => {
      const mockResult: AIAnalysisResult = {
        overallLevel: 6,
        overallTone: 'Castaño Natural',
        confidence: 94,
        zoneAnalysis: {
          roots: { level: 4, tone: 'Natural', grayPercentage: 15 },
          mids: { level: 6, tone: 'Dorado', condition: 'Bueno' },
          ends: { level: 7, tone: 'Cenizo', damage: 'Leve' },
        },
        recommendations: [
          'Aplicar desde medios a puntas para uniformidad',
          'Usar volumen 20 en raíces para cobertura de canas',
          'Tratamiento previo recomendado en puntas',
        ],
      };

      setAnalysisResult(mockResult);
      setIsAnalyzing(false);
      setCurrentPhase('validation');

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }, 3500);
  };

  const handleValidation = (field: string, isCorrect: boolean) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (isCorrect) {
      setValidationErrors(prev => prev.filter(e => e !== field));
    } else {
      setValidationErrors(prev => [...prev, field]);
    }
  };

  const handleConfirmation = () => {
    if (validationErrors.length === 0) {
      setCurrentPhase('confirmation');
      onUpdate({ diagnosisResult: analysisResult });

      setTimeout(() => {
        onNext();
      }, 1500);
    }
  };

  // Swipe gesture for phase navigation
  const swipeGesture = Gesture.Pan().onEnd(event => {
    if (event.velocityX > 500) {
      // Swipe right - go back
      onBack();
    } else if (event.velocityX < -500 && currentPhase === 'validation') {
      // Swipe left - continue
      handleConfirmation();
    }
  });

  const renderCapturePhase = () => {
    const captureTypes = [
      {
        type: 'roots' as const,
        label: 'Raíces',
        icon: '🔍',
        description: 'Vista frontal desde la coronilla',
      },
      {
        type: 'mids' as const,
        label: 'Medios',
        icon: '📐',
        description: 'Vista lateral del cabello',
      },
      {
        type: 'ends' as const,
        label: 'Puntas',
        icon: '✂️',
        description: 'Enfoque en las puntas',
      },
    ];

    return (
      <Animated.View entering={FadeIn} style={styles.phaseContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>📸 Diagnóstico Visual</Text>
          <Text style={styles.subtitle}>Cliente: {data.client?.name || 'Sin seleccionar'}</Text>
        </View>

        <View style={styles.captureGuide}>
          <Text style={styles.proTip}>💡 TÉCNICA PROFESIONAL</Text>
          <Text style={styles.guideText}>
            Captura en orden: raíces → medios → puntas para mejor análisis
          </Text>
        </View>

        <ScrollView showsVerticalScrollIndicator={false}>
          {captureTypes.map((item, index) => {
            const captured = captures.find(c => c.type === item.type);
            const isComplete = !!captured;

            return (
              <Animated.View
                key={item.type}
                entering={SlideInRight.delay(index * 100)}
                style={styles.captureCard}
              >
                <View style={[styles.captureButton, isComplete && styles.captureCompleted]}>
                  <View style={styles.captureHeader}>
                    <View style={styles.captureIcon}>
                      <Text style={styles.captureEmoji}>{item.icon}</Text>
                    </View>

                    <View style={styles.captureInfo}>
                      <Text style={styles.captureTitle}>{item.label}</Text>
                      <Text style={styles.captureDescription}>{item.description}</Text>
                    </View>

                    <View style={styles.captureAction}>
                      {isComplete ? (
                        <Animated.View style={[{ transform: [{ scale: successScale }] }]}>
                          <Check size={24} color={colors.success} />
                        </Animated.View>
                      ) : (
                        <EnhancedButton
                          variant="ghost"
                          size="sm"
                          icon={Camera}
                          iconPosition="only"
                          onPress={() => handlePhotoCapture(item.type)}
                          hapticFeedback="medium"
                          accessibilityLabel={`Capturar foto de ${item.label.toLowerCase()}`}
                        />
                      )}
                    </View>
                  </View>

                  {captured && (
                    <Animated.View entering={FadeIn.delay(200)} style={styles.capturedPreview}>
                      <Text style={styles.capturedTime}>
                        Capturado {captured.timestamp.toLocaleTimeString()}
                      </Text>
                    </Animated.View>
                  )}
                </View>
              </Animated.View>
            );
          })}
        </ScrollView>

        {captures.length >= 2 && (
          <Animated.View entering={FadeIn.delay(300)} style={styles.analyzeSection}>
            <EnhancedButton
              variant="primary"
              size="lg"
              icon={Zap}
              title="ANÁLISIS IA AVANZADO"
              onPress={startAnalysis}
              fullWidth
              hapticFeedback="heavy"
              animationType="spring"
              accessibilityHint="Inicia el análisis inteligente de las fotos capturadas"
            />
          </Animated.View>
        )}
      </Animated.View>
    );
  };

  const analysisProgressStyle = useAnimatedStyle(() => ({
    width: `${progressValue * 100}%`,
  }));

  const analysisPulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale }],
  }));

  const renderAnalysisPhase = () => {
    return (
      <Animated.View entering={FadeIn} style={styles.phaseContainer}>
        <View style={styles.analysisContainer}>
          <Animated.View style={[styles.analysisIcon, analysisPulseStyle]}>
            <Sparkles size={48} color={colors.primary} />
          </Animated.View>

          <Text style={styles.analysisTitle}>IA Analizando tu Cabello</Text>
          <Text style={styles.analysisSubtitle}>
            Detectando niveles, tonos y condición capilar...
          </Text>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, analysisProgressStyle]} />
            </View>
            <Text style={styles.progressText}>
              {isAnalyzing ? '94% Confianza' : 'Análisis completado'}
            </Text>
          </View>

          {!isAnalyzing && (
            <Animated.View entering={FadeIn.delay(500)}>
              <EnhancedButton
                variant="success"
                size="lg"
                icon={TrendingUp}
                title="VER RESULTADOS"
                onPress={() => setCurrentPhase('validation')}
                fullWidth
                hapticFeedback="medium"
              />
            </Animated.View>
          )}
        </View>
      </Animated.View>
    );
  };

  const renderValidationPhase = () => {
    if (!analysisResult) return null;

    return (
      <Animated.View entering={FadeIn} style={styles.phaseContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.resultsHeader}>
            <View style={styles.confidenceBadge}>
              <Award size={16} color={colors.success} />
              <Text style={styles.confidenceText}>Confianza: {analysisResult.confidence}%</Text>
            </View>

            <Text style={styles.resultsTitle}>Diagnóstico Completo</Text>
          </View>

          {/* Overall Analysis Card */}
          <Animated.View entering={SlideInRight.delay(100)} style={styles.analysisCard}>
            <View style={styles.cardHeader}>
              <Eye size={20} color={colors.primary} />
              <Text style={styles.cardTitle}>ANÁLISIS GENERAL</Text>
            </View>

            <View style={styles.overallResults}>
              <View style={styles.levelIndicator}>
                <Text style={styles.levelNumber}>{analysisResult.overallLevel}</Text>
                <Text style={styles.levelLabel}>Nivel Base</Text>
              </View>

              <View style={styles.toneInfo}>
                <Text style={styles.toneText}>{analysisResult.overallTone}</Text>
                <Text style={styles.toneLabel}>Tono Dominante</Text>
              </View>
            </View>

            <ValidationButtons
              onCorrect={() => handleValidation('overall', true)}
              onIncorrect={() => handleValidation('overall', false)}
              isValid={!validationErrors.includes('overall')}
            />
          </Animated.View>

          {/* Zone Analysis Cards */}
          {Object.entries(analysisResult.zoneAnalysis).map(([zone, data], index) => (
            <Animated.View
              key={zone}
              entering={SlideInRight.delay(200 + index * 100)}
              style={styles.zoneCard}
            >
              <View style={styles.cardHeader}>
                <Target size={16} color={colors.secondary} />
                <Text style={styles.cardTitle}>{zone.toUpperCase()}</Text>
              </View>

              <View style={styles.zoneData}>
                <Text style={styles.zoneLevel}>Nivel {data.level}</Text>
                <Text style={styles.zoneTone}>{data.tone}</Text>
                {'grayPercentage' in data && data.grayPercentage > 0 && (
                  <Text style={styles.grayText}>{data.grayPercentage}% canas</Text>
                )}
              </View>

              <ValidationButtons
                onCorrect={() => handleValidation(zone, true)}
                onIncorrect={() => handleValidation(zone, false)}
                isValid={!validationErrors.includes(zone)}
              />
            </Animated.View>
          ))}

          {/* Action Buttons */}
          <View style={styles.validationActions}>
            <EnhancedButton
              variant={validationErrors.length === 0 ? 'success' : 'outline'}
              size="lg"
              icon={Check}
              title={validationErrors.length === 0 ? 'CONFIRMAR DIAGNÓSTICO' : 'CORREGIR ERRORES'}
              onPress={handleConfirmation}
              fullWidth
              disabled={validationErrors.length > 0}
              hapticFeedback="heavy"
            />

            {validationErrors.length > 0 && (
              <EnhancedButton
                variant="ghost"
                size="base"
                icon={RefreshCw}
                title="REANALIZAR"
                onPress={startAnalysis}
                fullWidth
                hapticFeedback="light"
              />
            )}
          </View>
        </ScrollView>
      </Animated.View>
    );
  };

  const renderConfirmationPhase = () => (
    <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.confirmationContainer}>
      <Animated.View entering={FadeIn.delay(300)} style={styles.successIcon}>
        <Check size={64} color={colors.success} />
      </Animated.View>

      <Text style={styles.confirmationTitle}>¡Diagnóstico Confirmado!</Text>
      <Text style={styles.confirmationText}>
        Análisis guardado correctamente. Continuando al siguiente paso...
      </Text>
    </Animated.View>
  );

  return (
    <GestureDetector gesture={swipeGesture}>
      <View style={styles.container}>
        {currentPhase === 'capture' && renderCapturePhase()}
        {currentPhase === 'analysis' && renderAnalysisPhase()}
        {currentPhase === 'validation' && renderValidationPhase()}
        {currentPhase === 'confirmation' && renderConfirmationPhase()}
      </View>
    </GestureDetector>
  );
};

// Validation Buttons Component
const ValidationButtons: React.FC<{
  onCorrect: () => void;
  onIncorrect: () => void;
  isValid: boolean;
}> = ({ onCorrect, onIncorrect, isValid }) => (
  <View style={styles.validationButtons}>
    <EnhancedButton
      variant={isValid ? 'success' : 'outline'}
      size="sm"
      icon={Check}
      title="Correcto"
      onPress={onCorrect}
      hapticFeedback="light"
    />
    <EnhancedButton
      variant="outline"
      size="sm"
      icon={RefreshCw}
      title="Ajustar"
      onPress={onIncorrect}
      hapticFeedback="light"
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },

  phaseContainer: {
    flex: 1,
    padding: spacing['6'],
  },

  header: {
    marginBottom: spacing['6'],
  },

  title: {
    fontSize: typography.sizes['3xl'],
    fontWeight: typography.weights.extrabold,
    color: colors.text.primary,
    marginBottom: spacing['2'],
  },

  subtitle: {
    fontSize: typography.sizes.lg,
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },

  captureGuide: {
    backgroundColor: colors.primary + '10',
    borderRadius: radius.xl,
    padding: spacing['4'],
    marginBottom: spacing['6'],
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },

  proTip: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing['1'],
  },

  guideText: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    lineHeight: typography.sizes.base * typography.lineHeight.relaxed,
  },

  captureCard: {
    marginBottom: spacing['4'],
  },

  captureButton: {
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['5'],
    ...shadows.sm,
    borderWidth: 2,
    borderColor: 'transparent',
  },

  captureCompleted: {
    borderColor: colors.success,
    backgroundColor: colors.success + '08',
  },

  captureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  captureIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing['4'],
  },

  captureEmoji: {
    fontSize: 24,
  },

  captureInfo: {
    flex: 1,
  },

  captureTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
  },

  captureDescription: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
  },

  captureAction: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 44,
    height: 44,
  },

  capturedPreview: {
    marginTop: spacing['3'],
    paddingTop: spacing['3'],
    borderTopWidth: 1,
    borderTopColor: colors.neutral[200],
  },

  capturedTime: {
    fontSize: typography.sizes.xs,
    color: colors.text.tertiary,
    fontStyle: 'italic',
  },

  analyzeSection: {
    marginTop: spacing['6'],
  },

  // Analysis phase styles
  analysisContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing['8'],
  },

  analysisIcon: {
    width: 96,
    height: 96,
    borderRadius: radius.full,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['6'],
  },

  analysisTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing['2'],
  },

  analysisSubtitle: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing['8'],
  },

  progressContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: spacing['8'],
  },

  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: colors.neutral[200],
    borderRadius: radius.full,
    overflow: 'hidden',
    marginBottom: spacing['2'],
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: radius.full,
  },

  progressText: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },

  // Validation phase styles
  resultsHeader: {
    alignItems: 'center',
    marginBottom: spacing['6'],
  },

  confidenceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.success + '15',
    paddingHorizontal: spacing['3'],
    paddingVertical: spacing['1.5'],
    borderRadius: radius.full,
    marginBottom: spacing['3'],
  },

  confidenceText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.success,
    marginLeft: spacing['1.5'],
  },

  resultsTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
  },

  analysisCard: {
    backgroundColor: colors.card,
    borderRadius: radius.xl,
    padding: spacing['5'],
    marginBottom: spacing['4'],
    ...shadows.base,
  },

  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing['4'],
  },

  cardTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold,
    color: colors.text.secondary,
    marginLeft: spacing['2'],
    letterSpacing: 0.5,
  },

  overallResults: {
    flexDirection: 'row',
    marginBottom: spacing['4'],
  },

  levelIndicator: {
    alignItems: 'center',
    marginRight: spacing['6'],
  },

  levelNumber: {
    fontSize: typography.sizes['4xl'],
    fontWeight: typography.weights.extrabold,
    color: colors.primary,
  },

  levelLabel: {
    fontSize: typography.sizes.xs,
    color: colors.text.secondary,
    fontWeight: typography.weights.medium,
  },

  toneInfo: {
    flex: 1,
    justifyContent: 'center',
  },

  toneText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginBottom: spacing['1'],
  },

  toneLabel: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
  },

  zoneCard: {
    backgroundColor: colors.card,
    borderRadius: radius.lg,
    padding: spacing['4'],
    marginBottom: spacing['3'],
    ...shadows.sm,
  },

  zoneData: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing['3'],
  },

  zoneLevel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    marginRight: spacing['4'],
  },

  zoneTone: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    marginRight: spacing['3'],
  },

  grayText: {
    fontSize: typography.sizes.sm,
    color: colors.warning,
    fontWeight: typography.weights.medium,
  },

  validationButtons: {
    flexDirection: 'row',
    gap: spacing['2'],
  },

  validationActions: {
    gap: spacing['3'],
    marginTop: spacing['6'],
  },

  // Confirmation phase styles
  confirmationContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing['8'],
  },

  successIcon: {
    width: 120,
    height: 120,
    borderRadius: radius.full,
    backgroundColor: colors.success + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing['6'],
  },

  confirmationTitle: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.bold,
    color: colors.success,
    textAlign: 'center',
    marginBottom: spacing['3'],
  },

  confirmationText: {
    fontSize: typography.sizes.base,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: typography.sizes.base * typography.lineHeight.relaxed,
  },
});
