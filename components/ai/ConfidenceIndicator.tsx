import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import {
  CheckCircle,
  AlertTriangle,
  Brain,
  Edit3,
  X,
  TrendingUp,
  Eye,
  Shield,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { spacing } from '@/constants/theme';

interface ConfidenceIndicatorProps {
  confidence: number; // 0-100
  reasoning?: string[];
  analysisData?: Record<string, unknown>;
  onOverride?: (overrideData: Record<string, unknown>, justification: string) => void;
  context?: 'diagnosis' | 'formulation' | 'matching';
  showDetailed?: boolean;
}

type ConfidenceLevel = 'high' | 'medium' | 'low' | 'critical';

const CONFIDENCE_THRESHOLDS = {
  high: 85,
  medium: 70,
  low: 50,
};

const CONFIDENCE_MESSAGES = {
  high: '✅ La IA está muy segura',
  medium: '⚠️ La IA tiene buena confianza',
  low: '🔍 La IA sugiere revisar',
  critical: '⛔ Requiere validación manual',
};

const CONFIDENCE_EXPLANATIONS = {
  diagnosis: {
    high: 'Análisis claro con patrones reconocibles',
    medium: 'Algunos elementos requieren confirmación',
    low: 'Imagen poco clara o caso complejo',
    critical: 'Condiciones que requieren experto',
  },
  formulation: {
    high: 'Fórmula validada químicamente',
    medium: 'Fórmula estándar con precauciones',
    low: 'Combinación poco común',
    critical: 'Riesgo de incompatibilidad',
  },
  matching: {
    high: 'Coincidencia exacta encontrada',
    medium: 'Alternativas similares disponibles',
    low: 'Aproximación basada en características',
    critical: 'No se encontraron coincidencias',
  },
};

export const ConfidenceIndicator: React.FC<ConfidenceIndicatorProps> = ({
  confidence,
  reasoning = [],
  analysisData,
  onOverride,
  context = 'diagnosis',
  showDetailed = false,
}) => {
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [justification, setJustification] = useState('');
  const [showReasoningModal, setShowReasoningModal] = useState(false);

  const scaleAnim = useSharedValue(0);
  const progressAnim = useSharedValue(0);
  const pulseAnim = useSharedValue(1);

  React.useEffect(() => {
    scaleAnim.value = withSpring(1, { damping: 12 });
    progressAnim.value = withTiming(confidence / 100, { duration: 1000 });

    // Pulse animation for low confidence
    if (getConfidenceLevel(confidence) === 'critical') {
      pulseAnim.value = withTiming(1.1, { duration: 500 });
      setTimeout(() => {
        pulseAnim.value = withTiming(1, { duration: 500 });
      }, 500);
    }
  }, [confidence, progressAnim, scaleAnim, pulseAnim]);

  const getConfidenceLevel = (conf: number): ConfidenceLevel => {
    if (conf >= CONFIDENCE_THRESHOLDS.high) return 'high';
    if (conf >= CONFIDENCE_THRESHOLDS.medium) return 'medium';
    if (conf >= CONFIDENCE_THRESHOLDS.low) return 'low';
    return 'critical';
  };

  const getConfidenceColor = (level: ConfidenceLevel) => {
    switch (level) {
      case 'high':
        return Colors.light.success;
      case 'medium':
        return Colors.light.warning;
      case 'low':
        return Colors.light.error;
      case 'critical':
        return '#FF4444';
    }
  };

  const level = getConfidenceLevel(confidence);
  const color = getConfidenceColor(level);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scaleAnim }, { scale: pulseAnim }],
  }));

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${interpolate(progressAnim, [0, 1], [0, 100])}%`,
  }));

  const handleOverrideSubmit = useCallback(() => {
    if (justification.trim().length < 10) {
      Alert.alert('Error', 'La justificación debe tener al menos 10 caracteres');
      return;
    }

    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    onOverride?.(analysisData, justification);
    setShowOverrideModal(false);
    setJustification('');
  }, [justification, analysisData, onOverride]);

  const handleShowReasoning = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowReasoningModal(true);
  }, []);

  const renderIcon = () => {
    switch (level) {
      case 'high':
        return <CheckCircle size={20} color={color} />;
      case 'medium':
        return <TrendingUp size={20} color={color} />;
      case 'low':
        return <AlertTriangle size={20} color={color} />;
      case 'critical':
        return <Shield size={20} color={color} />;
    }
  };

  const renderReasoningModal = () => (
    <Modal
      visible={showReasoningModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowReasoningModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>🧠 Razonamiento de la IA</Text>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowReasoningModal(false)}
          >
            <X size={24} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.confidenceOverview}>
            <Text style={styles.confidenceTitle}>Nivel de confianza: {confidence}%</Text>
            <Text style={styles.confidenceExplanation}>
              {CONFIDENCE_EXPLANATIONS[context][level]}
            </Text>
          </View>

          <View style={styles.reasoningSection}>
            <Text style={styles.reasoningTitle}>💡 Factores analizados:</Text>
            {reasoning.length > 0 ? (
              reasoning.map((reason, index) => (
                <View key={index} style={styles.reasoningItem}>
                  <Text style={styles.reasoningBullet}>•</Text>
                  <Text style={styles.reasoningText}>{reason}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.noReasoningText}>No hay detalles específicos disponibles</Text>
            )}
          </View>

          {analysisData && (
            <View style={styles.dataSection}>
              <Text style={styles.dataSectionTitle}>🔬 Datos del análisis:</Text>
              <View style={styles.dataGrid}>
                {Object.entries(analysisData)
                  .slice(0, 6)
                  .map(([key, value]) => (
                    <View key={key} style={styles.dataItem}>
                      <Text style={styles.dataKey}>{key}:</Text>
                      <Text style={styles.dataValue}>
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          <View style={styles.aiDisclaimer}>
            <Brain size={16} color={Colors.light.textSecondary} />
            <Text style={styles.aiDisclaimerText}>
              La IA es una herramienta de apoyo. Siempre valida los resultados con tu experiencia
              profesional.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const renderOverrideModal = () => (
    <Modal
      visible={showOverrideModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowOverrideModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>✏️ Override Manual</Text>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowOverrideModal(false)}
          >
            <X size={24} color={Colors.light.textSecondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <Text style={styles.overrideExplanation}>
            Como profesional, puedes sobrescribir el análisis de la IA. Explica brevemente por qué
            consideras que la IA se equivocó.
          </Text>

          <View style={styles.currentAnalysis}>
            <Text style={styles.currentAnalysisTitle}>Análisis actual de la IA:</Text>
            <Text style={styles.currentAnalysisText}>
              Confianza: {confidence}% ({CONFIDENCE_MESSAGES[level]})
            </Text>
          </View>

          <View style={styles.justificationContainer}>
            <Text style={styles.justificationLabel}>Justificación (mínimo 10 caracteres):</Text>
            <TextInput
              style={styles.justificationInput}
              multiline
              numberOfLines={4}
              value={justification}
              onChangeText={setJustification}
              placeholder="Ejemplo: La iluminación de la foto no es óptima y puedo ver claramente que el nivel es 6, no 4 como indica la IA..."
              placeholderTextColor={Colors.light.textSecondary}
            />
            <Text style={styles.characterCount}>{justification.length}/10 caracteres mínimos</Text>
          </View>

          <View style={styles.overrideActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => setShowOverrideModal(false)}
            >
              <Text style={styles.cancelButtonText}>Cancelar</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.confirmButton,
                justification.length < 10 && styles.confirmButtonDisabled,
              ]}
              onPress={handleOverrideSubmit}
              disabled={justification.length < 10}
            >
              <Text style={styles.confirmButtonText}>Confirmar Override</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  return (
    <Animated.View style={[styles.container, animatedContainerStyle]}>
      <View style={[styles.confidenceCard, { borderLeftColor: color }]}>
        <View style={styles.confidenceHeader}>
          <View style={styles.confidenceIcon}>{renderIcon()}</View>

          <View style={styles.confidenceInfo}>
            <Text style={styles.confidenceMessage}>{CONFIDENCE_MESSAGES[level]}</Text>
            <Text style={styles.confidencePercentage}>Confianza: {confidence}%</Text>
          </View>

          <View style={styles.confidenceActions}>
            {reasoning.length > 0 && (
              <TouchableOpacity style={styles.actionButton} onPress={handleShowReasoning}>
                <Eye size={16} color={Colors.light.primary} />
              </TouchableOpacity>
            )}

            {onOverride && level !== 'high' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setShowOverrideModal(true)}
              >
                <Edit3 size={16} color={Colors.light.warning} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {showDetailed && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View
                style={[styles.progressFill, { backgroundColor: color }, animatedProgressStyle]}
              />
            </View>
            <Text style={styles.progressText}>{CONFIDENCE_EXPLANATIONS[context][level]}</Text>
          </View>
        )}
      </View>

      {renderReasoningModal()}
      {renderOverrideModal()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: spacing.sm,
  },
  confidenceCard: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: spacing.md,
    borderLeftWidth: 4,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  confidenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  confidenceIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confidenceInfo: {
    flex: 1,
  },
  confidenceMessage: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 2,
  },
  confidencePercentage: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  confidenceActions: {
    flexDirection: 'row',
    gap: spacing.xs,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    marginTop: spacing.md,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.light.surface,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },

  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.surface,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.surface,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    padding: spacing.lg,
  },

  // Reasoning modal
  confidenceOverview: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  confidenceTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  confidenceExplanation: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  reasoningSection: {
    marginBottom: spacing.lg,
  },
  reasoningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  reasoningItem: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  reasoningBullet: {
    width: 20,
    fontSize: 16,
    color: Colors.light.primary,
  },
  reasoningText: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  noReasoningText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  dataSection: {
    marginBottom: spacing.lg,
  },
  dataSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  dataGrid: {
    backgroundColor: Colors.light.surface,
    borderRadius: 8,
    padding: spacing.md,
  },
  dataItem: {
    flexDirection: 'row',
    marginBottom: spacing.xs,
  },
  dataKey: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.light.textSecondary,
    minWidth: 100,
  },
  dataValue: {
    flex: 1,
    fontSize: 12,
    color: Colors.light.text,
  },
  aiDisclaimer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.xs,
    backgroundColor: Colors.light.warningLight || '#FFF8E1',
    padding: spacing.md,
    borderRadius: 8,
    marginTop: spacing.lg,
  },
  aiDisclaimerText: {
    flex: 1,
    fontSize: 12,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },

  // Override modal
  overrideExplanation: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginBottom: spacing.lg,
  },
  currentAnalysis: {
    backgroundColor: Colors.light.warningBackground || '#FFF3CD',
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  currentAnalysisTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  currentAnalysisText: {
    fontSize: 14,
    color: Colors.light.text,
  },
  justificationContainer: {
    marginBottom: spacing.xl,
  },
  justificationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: spacing.sm,
  },
  justificationInput: {
    borderWidth: 1,
    borderColor: Colors.light.surface,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: 14,
    color: Colors.light.text,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'right',
  },
  overrideActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: Colors.light.textSecondary,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.textSecondary,
  },
  confirmButton: {
    flex: 1,
    backgroundColor: Colors.light.warning,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    backgroundColor: Colors.light.surface,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.surface,
  },
});
