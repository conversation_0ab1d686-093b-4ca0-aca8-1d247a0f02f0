import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Text, StyleSheet, Animated, ActivityIndicator } from 'react-native';
import { Check, CloudOff, AlertCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useHaptics } from '@/hooks/useHaptics';

interface AutoSaveIndicatorProps {
  lastSaved?: Date;
  isSaving?: boolean;
  hasConflict?: boolean;
  isOffline?: boolean;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
  lastSaved,
  isSaving = false,
  hasConflict = false,
  isOffline = false,
}) => {
  const [fadeAnim] = useState(() => new Animated.Value(0));
  const [pulseAnim] = useState(() => new Animated.Value(1));
  const [showSaved, setShowSaved] = useState(false);
  const mountedRef = useRef(true);
  const lastSavedRef = useRef<Date | undefined>(lastSaved);
  const haptics = useHaptics();

  // Pulse animation when saving
  useEffect(() => {
    if (isSaving) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      );
      animation.start();
      return () => animation.stop();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isSaving, pulseAnim]);

  // Handle fade animations
  const fadeIn = useCallback(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const fadeOut = useCallback(() => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      if (mountedRef.current) {
        setShowSaved(false);
      }
    });
  }, [fadeAnim]);

  // Show/hide animation when saving state changes
  useEffect(() => {
    if (isSaving || hasConflict || isOffline) {
      fadeIn();
    } else if (showSaved) {
      fadeIn();
      const timer = setTimeout(() => {
        if (mountedRef.current) {
          fadeOut();
        }
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isSaving, showSaved, hasConflict, isOffline, fadeIn, fadeOut]);

  // Track when saving completes
  useEffect(() => {
    const currentLastSaved = lastSaved?.getTime();
    const previousLastSaved = lastSavedRef.current?.getTime();

    if (!isSaving &&
        lastSaved &&
        currentLastSaved !== previousLastSaved &&
        !hasConflict &&
        !isOffline) {
      setShowSaved(true);
    }

    lastSavedRef.current = lastSaved;
  }, [isSaving, lastSaved, hasConflict, isOffline]);

  // Track mounted state
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  if (!isSaving && !showSaved && !hasConflict && !isOffline) {
    return null;
  }

  const getContent = () => {
    if (hasConflict) {
      return {
        icon: <AlertCircle size={14} color={Colors.light.warning} />,
        text: 'Conflicto detectado',
        textStyle: styles.conflictText,
        containerStyle: [
          styles.conflictContainer,
          { backgroundColor: `${Colors.light.warning}10` },
        ],
      };
    }

    if (isOffline) {
      return {
        icon: <CloudOff size={14} color={Colors.light.gray} />,
        text: 'Sin conexión',
        textStyle: styles.offlineText,
        containerStyle: styles.offlineContainer,
      };
    }

    if (isSaving) {
      return {
        icon: <ActivityIndicator size="small" color={Colors.light.primary} />,
        text: 'Guardando...',
        textStyle: styles.savingText,
        containerStyle: [styles.savingContainer, { backgroundColor: `${Colors.light.primary}10` }],
      };
    }

    return {
      icon: <Check size={14} color={Colors.light.success} />,
      text: 'Guardado',
      textStyle: styles.savedText,
      containerStyle: [styles.savedContainer, { backgroundColor: `${Colors.light.success}10` }],
    };
  };

  const content = getContent();

  return (
    <Animated.View
      style={[
        styles.container,
        content.containerStyle,
        {
          opacity: fadeAnim,
          transform: [{ scale: isSaving ? pulseAnim : 1 }],
        },
      ]}
    >
      {content.icon}
      <Text style={[styles.text, content.textStyle]}>{content.text}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
    borderWidth: 1,
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
  savingContainer: {
    borderColor: Colors.light.primary,
  },
  savingText: {
    color: Colors.light.primary,
  },
  savedContainer: {
    borderColor: Colors.light.success,
  },
  savedText: {
    color: Colors.light.success,
  },
  conflictContainer: {
    borderColor: Colors.light.warning,
  },
  conflictText: {
    color: Colors.light.warning,
  },
  offlineContainer: {
    backgroundColor: Colors.light.surface,
    borderColor: Colors.light.border,
  },
  offlineText: {
    color: Colors.light.gray,
  },
});
