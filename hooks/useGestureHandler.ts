import { useRef, useCallback } from 'react';
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
  GestureUpdateEvent,
  PanGestureHandlerEventPayload,
} from 'react-native-gesture-handler';
import {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
  Extrapolate,
  runOnJS,
} from 'react-native-reanimated';
import { Dimensions } from 'react-native';
import { useHaptics } from './useHaptics';

const { width: _SCREEN_WIDTH } = Dimensions.get('window');

interface UseGestureHandlerProps {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  swipeEnabled?: boolean;
  scrollPriority?: 'vertical' | 'horizontal' | 'both';
  swipeThreshold?: number;
  swipeVelocity?: number;
}

export const useGestureHandler = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  swipeEnabled = true,
  scrollPriority = 'vertical',
  swipeThreshold = _SCREEN_WIDTH * 0.25,
  swipeVelocity = 0.3,
}: UseGestureHandlerProps) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const startX = useSharedValue(0);
  const startY = useSharedValue(0);
  const haptics = useHaptics();
  const gestureRef = useRef(null);

  const handleSwipeComplete = useCallback(
    (direction: 'left' | 'right' | 'up' | 'down') => {
      'worklet';

      runOnJS(() => {
        haptics.impact('light');

        switch (direction) {
          case 'left':
            onSwipeLeft?.();
            break;
          case 'right':
            onSwipeRight?.();
            break;
          case 'up':
            onSwipeUp?.();
            break;
          case 'down':
            onSwipeDown?.();
            break;
        }
      })();
    },
    [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, haptics]
  );

  const panGesture = Gesture.Pan()
    .enabled(swipeEnabled)
    .onStart(event => {
      startX.value = event.translationX;
      startY.value = event.translationY;
    })
    .onUpdate((event: GestureUpdateEvent<PanGestureHandlerEventPayload>) => {
      const deltaX = event.translationX - startX.value;
      const deltaY = event.translationY - startY.value;

      // Determine if this is primarily a horizontal or vertical gesture
      const isHorizontal = Math.abs(deltaX) > Math.abs(deltaY);

      // Apply movement based on scroll priority
      if (scrollPriority === 'vertical' && isHorizontal) {
        translateX.value = deltaX;
        translateY.value = 0;
      } else if (scrollPriority === 'horizontal' && !isHorizontal) {
        translateX.value = 0;
        translateY.value = deltaY;
      } else if (scrollPriority === 'both') {
        translateX.value = deltaX;
        translateY.value = deltaY;
      }
    })
    .onEnd(event => {
      const velocityX = event.velocityX;
      const velocityY = event.velocityY;
      const deltaX = translateX.value;
      const deltaY = translateY.value;

      // Check horizontal swipe
      if (Math.abs(deltaX) > swipeThreshold || Math.abs(velocityX) > swipeVelocity * 1000) {
        if (deltaX < 0 && onSwipeLeft) {
          handleSwipeComplete('left');
        } else if (deltaX > 0 && onSwipeRight) {
          handleSwipeComplete('right');
        }
      }

      // Check vertical swipe
      if (Math.abs(deltaY) > swipeThreshold || Math.abs(velocityY) > swipeVelocity * 1000) {
        if (deltaY < 0 && onSwipeUp) {
          handleSwipeComplete('up');
        } else if (deltaY > 0 && onSwipeDown) {
          handleSwipeComplete('down');
        }
      }

      // Reset position
      translateX.value = withSpring(0, {
        damping: 20,
        stiffness: 300,
      });
      translateY.value = withSpring(0, {
        damping: 20,
        stiffness: 300,
      });
    });

  // Special handling for vertical scroll priority
  if (scrollPriority === 'vertical') {
    panGesture
      .simultaneousWithExternalGesture(gestureRef)
      .shouldCancelWhenOutside(false)
      .minPointers(1)
      .maxPointers(1)
      .failOffsetY([-10, 10]) // Allow vertical scroll to take over quickly
      .activeOffsetX([-20, 20]); // Require significant horizontal movement
  }

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX }, { translateY: translateY }],
    };
  });

  const animatedOpacityStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      Math.abs(translateX),
      [0, swipeThreshold],
      [1, 0.5],
      Extrapolate.CLAMP
    );

    return { opacity };
  });

  return {
    gesture: panGesture,
    animatedStyle,
    animatedOpacityStyle,
    translateX,
    translateY,
    GestureHandlerRootView,
    GestureDetector,
  };
};
